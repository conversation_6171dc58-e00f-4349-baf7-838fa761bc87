<template>
    <div class="dominos-order-container">
        <!-- 主要内容区域 - 左右布局 -->
        <div class="main-content">
            <!-- 左侧：填写下单信息 -->
            <div class="left-panel">
                <div class="order-form">
                    <!-- 标题区域 -->
                    <div class="form-header">
                        <div class="header-icon">
                            <el-icon size="20"><Document /></el-icon>
                        </div>
                        <h3 class="form-title">填写下单信息</h3>
                    </div>

                    <!-- 进度指示器 -->
                    <div class="progress-indicator">
                        <div class="progress-step active">
                            <div class="step-number">1</div>
                            <span class="step-text">配送信息</span>
                        </div>
                        <div class="progress-line"></div>
                        <div class="progress-step">
                            <div class="step-number">2</div>
                            <span class="step-text">确认订单</span>
                        </div>
                    </div>

                    <el-form :model="orderForm" label-width="90px" class="form-content">
                        <!-- 配送区域 -->
                        <div class="form-section">
                            <div class="section-title">
                                <el-icon class="section-icon"><MapLocation /></el-icon>
                                <span>配送区域</span>
                            </div>

                            <!-- 送餐城市 -->
                            <el-form-item label="送餐城市:" class="form-item">
                                <el-select
                                    v-model="orderForm.deliveryCity"
                                    placeholder="请选择城市"
                                    class="form-input"
                                    clearable
                                >
                                    <el-option label="上海市" value="shanghai">
                                        <span class="option-text">
                                            <el-icon><Location /></el-icon>
                                            上海市
                                        </span>
                                    </el-option>
                                    <el-option label="北京市" value="beijing">
                                        <span class="option-text">
                                            <el-icon><Location /></el-icon>
                                            北京市
                                        </span>
                                    </el-option>
                                    <el-option label="广州市" value="guangzhou">
                                        <span class="option-text">
                                            <el-icon><Location /></el-icon>
                                            广州市
                                        </span>
                                    </el-option>
                                    <el-option label="深圳市" value="shenzhen">
                                        <span class="option-text">
                                            <el-icon><Location /></el-icon>
                                            深圳市
                                        </span>
                                    </el-option>
                                </el-select>
                            </el-form-item>

                            <!-- 送餐地址 -->
                            <el-form-item label="送餐地址:" class="form-item">
                                <div class="delivery-address-group">
                                    <el-input
                                        v-model="orderForm.deliveryAddress"
                                        placeholder="地址或小区写字楼或学校"
                                        class="delivery-input"
                                        prefix-icon="Search"
                                    />
                                    <el-button type="primary" class="search-btn" :icon="Search">
                                        搜索
                                    </el-button>
                                </div>
                            </el-form-item>

                            <!-- 详细地址 -->
                            <el-form-item label="详细地址:" class="form-item">
                                <el-input
                                    v-model="orderForm.detailAddress"
                                    placeholder="楼号/门牌号，例：1号101室"
                                    class="form-input"
                                    prefix-icon="OfficeBuilding"
                                />
                            </el-form-item>

                            <!-- 提示信息 -->
                            <div class="warning-text">
                                <el-icon class="warning-icon"><WarningFilled /></el-icon>
                                <span>如该号地址和所选这个区域没有了，现场无法交付单</span>
                            </div>
                        </div>

                        <!-- 联系信息 -->
                        <div class="form-section">
                            <div class="section-title">
                                <el-icon class="section-icon"><User /></el-icon>
                                <span>联系信息</span>
                            </div>

                            <!-- 收货人 -->
                            <el-form-item label="*收货人:" class="form-item required">
                                <el-input
                                    v-model="orderForm.receiverName"
                                    placeholder="请输入收货人姓名"
                                    class="form-input"
                                    prefix-icon="Avatar"
                                />
                            </el-form-item>

                            <!-- 联系电话 -->
                            <el-form-item label="*联系电话:" class="form-item required">
                                <el-input
                                    v-model="orderForm.phone"
                                    placeholder="请输入联系电话"
                                    class="form-input"
                                    prefix-icon="Phone"
                                />
                            </el-form-item>
                        </div>

                        <!-- 门店信息 -->
                        <div class="form-section">
                            <div class="section-title">
                                <el-icon class="section-icon"><Shop /></el-icon>
                                <span>门店信息</span>
                            </div>

                            <!-- 送餐门店 -->
                            <el-form-item label="*送餐门店:" class="form-item required">
                                <el-input
                                    v-model="orderForm.store"
                                    placeholder="系统将自动匹配最近门店"
                                    class="form-input"
                                    prefix-icon="Shop"
                                    readonly
                                />
                            </el-form-item>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="form-actions">
                            <el-button
                                type="default"
                                size="large"
                                class="reset-btn"
                                :icon="RefreshLeft"
                                @click="resetForm"
                            >
                                重置表单
                            </el-button>
                            <el-button
                                type="primary"
                                size="large"
                                class="submit-btn"
                                :icon="Check"
                                :disabled="!isFormValid"
                            >
                                确认信息
                            </el-button>
                        </div>
                    </el-form>
                </div>
            </div>

            <!-- 右侧：快速识别地址 -->
            <div class="right-panel">
                <div class="address-recognition">
                    <!-- 标题区域 -->
                    <div class="recognition-header">
                        <div class="header-icon">
                            <el-icon size="20"><Location /></el-icon>
                        </div>
                        <h3 class="recognition-title">快速识别地址</h3>
                    </div>

                    <!-- 功能说明 -->
                    <div class="feature-description">
                        <div class="feature-item">
                            <el-icon class="feature-icon"><Check /></el-icon>
                            <span>自动识别收件人姓名</span>
                        </div>
                        <div class="feature-item">
                            <el-icon class="feature-icon"><Check /></el-icon>
                            <span>智能提取联系电话</span>
                        </div>
                        <div class="feature-item">
                            <el-icon class="feature-icon"><Check /></el-icon>
                            <span>精准解析收货地址</span>
                        </div>
                    </div>

                    <!-- 提示信息 -->
                    <div class="tip-text">
                        <el-icon class="tip-icon"><InfoFilled /></el-icon>
                        <div class="tip-content">
                            <div class="tip-title">使用说明</div>
                            <div class="tip-desc">粘贴包含收件人姓名、手机号、收货地址的完整信息，系统将自动识别并填充到左侧表单中</div>
                        </div>
                    </div>

                    <!-- 识别文本区域 -->
                    <div class="recognition-area">
                        <div class="textarea-label">
                            <el-icon><EditPen /></el-icon>
                            <span>粘贴收件人信息</span>
                        </div>
                        <el-input
                            v-model="recognitionText"
                            type="textarea"
                            :rows="8"
                            placeholder="例如：张三 13812345678 上海市浦东新区陆家嘴环路1000号恒生银行大厦28楼2801室"
                            class="recognition-textarea"
                        />
                        <div class="textarea-footer">
                            <span class="char-count">{{ recognitionText.length }}/500</span>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="recognition-actions">
                        <el-button
                            type="default"
                            @click="clearRecognition"
                            :icon="Delete"
                            class="clear-btn"
                        >
                            清除内容
                        </el-button>
                        <el-button
                            type="primary"
                            @click="recognizeAddress"
                            :icon="MagicStick"
                            class="recognize-btn"
                            :disabled="!recognitionText.trim()"
                        >
                            一键识别
                        </el-button>
                    </div>

                    <!-- 识别结果预览 -->
                    <div class="recognition-result" v-if="showResult">
                        <div class="result-header">
                            <el-icon class="result-icon"><SuccessFilled /></el-icon>
                            <span class="result-title">识别结果</span>
                        </div>
                        <div class="result-content">
                            <div class="result-item" v-if="lastRecognitionResult.receiverName">
                                <label>收件人：</label>
                                <span>{{ lastRecognitionResult.receiverName }}</span>
                            </div>
                            <div class="result-item" v-if="lastRecognitionResult.phone">
                                <label>联系电话：</label>
                                <span>{{ lastRecognitionResult.phone }}</span>
                            </div>
                            <div class="result-item" v-if="lastRecognitionResult.deliveryAddress">
                                <label>收货地址：</label>
                                <span>{{ lastRecognitionResult.deliveryAddress }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import {
    Location, Check, InfoFilled, EditPen, Delete, MagicStick, SuccessFilled,
    Document, MapLocation, Search, OfficeBuilding, WarningFilled, User,
    Avatar, Phone, Shop, RefreshLeft
} from '@element-plus/icons-vue'

// 订单表单数据
const orderForm = reactive({
    deliveryCity: 'shanghai',
    deliveryAddress: '',
    detailAddress: '',
    receiverName: '',
    phone: '',
    store: ''
})

// 识别文本
const recognitionText = ref('')

// 显示识别结果
const showResult = ref(false)

// 最后一次识别结果
const lastRecognitionResult = reactive({
    receiverName: '',
    phone: '',
    deliveryAddress: ''
})

// 表单验证
const isFormValid = computed(() => {
    return orderForm.receiverName && orderForm.phone && orderForm.deliveryCity
})

// 重置表单
const resetForm = () => {
    orderForm.deliveryCity = 'shanghai'
    orderForm.deliveryAddress = ''
    orderForm.detailAddress = ''
    orderForm.receiverName = ''
    orderForm.phone = ''
    orderForm.store = ''
}

// 添加客户功能
const addCustomer = () => {
    console.log('添加客户:', orderForm.receiverName)
    // TODO: 实现添加客户逻辑
}

// 搜索地址功能
const searchAddress = () => {
    console.log('搜索地址:', orderForm.deliveryAddress)
    // TODO: 实现地址搜索逻辑
}

// 清除识别文本
const clearRecognition = () => {
    recognitionText.value = ''
    showResult.value = false
    // 清空识别结果
    lastRecognitionResult.receiverName = ''
    lastRecognitionResult.phone = ''
    lastRecognitionResult.deliveryAddress = ''
}

// 一键识别地址
const recognizeAddress = () => {
    if (!recognitionText.value.trim()) {
        return
    }

    // 简单的地址识别逻辑（可以根据实际需求完善）
    const text = recognitionText.value

    // 识别手机号
    const phoneMatch = text.match(/1[3-9]\d{9}/)
    if (phoneMatch) {
        orderForm.phone = phoneMatch[0]
    }

    // 识别姓名（假设姓名在手机号前面，2-4个字符）
    const nameMatch = text.match(/([^\d\s]{2,4})\s*1[3-9]\d{9}/)
    if (nameMatch) {
        orderForm.customerName = nameMatch[1]
    }

    // 识别地址（包含省市区的部分）
    const addressMatch = text.match(/([\u4e00-\u9fa5]+[省市区县][\u4e00-\u9fa5\d\s]+)/)
    if (addressMatch) {
        orderForm.deliveryAddress = addressMatch[1]
    }

    // 识别收货人姓名（假设姓名在手机号前面，2-4个字符）
    if (nameMatch) {
        orderForm.receiverName = nameMatch[1]
        lastRecognitionResult.receiverName = nameMatch[1]
    }

    // 保存识别结果用于预览
    if (phoneMatch) {
        lastRecognitionResult.phone = phoneMatch[0]
    }
    if (addressMatch) {
        lastRecognitionResult.deliveryAddress = addressMatch[1]
    }

    // 显示识别结果
    showResult.value = true

    console.log('识别结果:', orderForm)
}
</script>

<style scoped lang="scss">
.dominos-order-container {
    padding: 20px;
    background-color: #f5f5f5;
    min-height: 100vh;

    .main-content {
        display: flex;
        gap: 20px;
        align-items: flex-start;

        .left-panel {
            flex: 1;
            width: 50%;
            min-width: 400px;

            .order-form {
                background: white;
                padding: 30px;
                border-radius: 12px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                height: 100%;
                border: 1px solid #e8f4fd;

                .form-header {
                    display: flex;
                    align-items: center;
                    margin-bottom: 25px;
                    padding-bottom: 15px;
                    border-bottom: 2px solid #f0f9ff;

                    .header-icon {
                        width: 40px;
                        height: 40px;
                        background: linear-gradient(135deg, #409eff, #67c23a);
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: white;
                        margin-right: 12px;
                    }

                    .form-title {
                        margin: 0;
                        color: #333;
                        font-size: 18px;
                        font-weight: 600;
                    }
                }

                .progress-indicator {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-bottom: 30px;
                    padding: 20px;
                    background: linear-gradient(135deg, #f0f9ff, #e8f4fd);
                    border-radius: 8px;

                    .progress-step {
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        position: relative;

                        .step-number {
                            width: 32px;
                            height: 32px;
                            border-radius: 50%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-weight: 600;
                            font-size: 14px;
                            margin-bottom: 8px;
                            background: #e4e7ed;
                            color: #909399;
                            transition: all 0.3s ease;
                        }

                        .step-text {
                            font-size: 12px;
                            color: #909399;
                            transition: all 0.3s ease;
                        }

                        &.active {
                            .step-number {
                                background: linear-gradient(135deg, #409eff, #67c23a);
                                color: white;
                            }

                            .step-text {
                                color: #409eff;
                                font-weight: 600;
                            }
                        }
                    }

                    .progress-line {
                        width: 60px;
                        height: 2px;
                        background: #e4e7ed;
                        margin: 0 20px;
                        position: relative;
                        top: -16px;
                    }
                }

                .form-content {
                    .form-section {
                        margin-bottom: 30px;
                        padding: 20px;
                        background: #fafbfc;
                        border-radius: 8px;
                        border: 1px solid #f0f0f0;

                        .section-title {
                            display: flex;
                            align-items: center;
                            margin-bottom: 20px;
                            font-weight: 600;
                            color: #333;
                            font-size: 15px;

                            .section-icon {
                                margin-right: 8px;
                                color: #409eff;
                                font-size: 18px;
                            }
                        }
                    }

                    .form-item {
                        margin-bottom: 18px;

                        &.required {
                            :deep(.el-form-item__label) {
                                color: #f56c6c;
                                font-weight: 600;
                            }
                        }
                    }

                    .form-input {
                        width: 100%;

                        :deep(.el-input__wrapper) {
                            border-radius: 8px;
                            border: 2px solid #e8f4fd;
                            transition: all 0.3s ease;

                            &:hover {
                                border-color: #409eff;
                            }
                        }

                        :deep(.el-input__inner) {
                            font-size: 14px;
                        }
                    }

                    .delivery-address-group {
                        display: flex;
                        gap: 12px;
                        align-items: center;

                        .delivery-input {
                            flex: 1;
                        }

                        .search-btn {
                            flex-shrink: 0;
                            height: 40px;
                            border-radius: 8px;
                            background: linear-gradient(135deg, #409eff, #67c23a);
                            border: none;
                            font-weight: 600;
                            transition: all 0.3s ease;

                            &:hover {
                                transform: translateY(-2px);
                                box-shadow: 0 6px 16px rgba(64, 158, 255, 0.3);
                            }
                        }
                    }

                    .warning-text {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: #f56c6c;
                        font-size: 14px;
                        margin: 20px 0;
                        padding: 15px;
                        background: linear-gradient(135deg, #fef0f0, #fde2e2);
                        border-radius: 8px;
                        border-left: 4px solid #f56c6c;

                        .warning-icon {
                            margin-right: 8px;
                            font-size: 16px;
                        }
                    }

                    .form-actions {
                        display: flex;
                        gap: 15px;
                        margin-top: 30px;
                        padding-top: 20px;
                        border-top: 1px solid #f0f0f0;

                        .reset-btn {
                            flex: 1;
                            height: 45px;
                            border-radius: 8px;
                            border: 2px solid #909399;
                            color: #909399;
                            background: white;
                            font-weight: 600;
                            transition: all 0.3s ease;

                            &:hover {
                                background: #909399;
                                color: white;
                            }
                        }

                        .submit-btn {
                            flex: 2;
                            height: 45px;
                            border-radius: 8px;
                            background: linear-gradient(135deg, #409eff, #67c23a);
                            border: none;
                            font-weight: 600;
                            font-size: 16px;
                            transition: all 0.3s ease;

                            &:hover:not(:disabled) {
                                transform: translateY(-2px);
                                box-shadow: 0 8px 20px rgba(64, 158, 255, 0.4);
                            }

                            &:disabled {
                                background: #c0c4cc;
                                cursor: not-allowed;
                                transform: none;
                                box-shadow: none;
                            }
                        }
                    }
                }
            }
        }

        .right-panel {
            flex: 1;
            width: 50%;
            min-width: 400px;

            .address-recognition {
                background: white;
                padding: 30px;
                border-radius: 12px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                height: 100%;
                border: 1px solid #e8f4fd;

                .recognition-header {
                    display: flex;
                    align-items: center;
                    margin-bottom: 25px;
                    padding-bottom: 15px;
                    border-bottom: 2px solid #f0f9ff;

                    .header-icon {
                        width: 40px;
                        height: 40px;
                        background: linear-gradient(135deg, #409eff, #67c23a);
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: white;
                        margin-right: 12px;
                    }

                    .recognition-title {
                        margin: 0;
                        color: #333;
                        font-size: 18px;
                        font-weight: 600;
                    }
                }

                .feature-description {
                    margin-bottom: 25px;
                    padding: 20px;
                    background: linear-gradient(135deg, #f0f9ff, #e8f4fd);
                    border-radius: 8px;
                    border: 1px solid #d4edda;

                    .feature-item {
                        display: flex;
                        align-items: center;
                        margin-bottom: 8px;
                        font-size: 14px;
                        color: #495057;

                        &:last-child {
                            margin-bottom: 0;
                        }

                        .feature-icon {
                            color: #67c23a;
                            margin-right: 8px;
                            font-size: 16px;
                        }
                    }
                }

                .tip-text {
                    display: flex;
                    align-items: flex-start;
                    margin-bottom: 25px;
                    padding: 18px;
                    background: linear-gradient(135deg, #fff7e6, #fef3e2);
                    border-radius: 8px;
                    border-left: 4px solid #fa8c16;

                    .tip-icon {
                        color: #fa8c16;
                        margin-right: 12px;
                        margin-top: 2px;
                        font-size: 18px;
                        flex-shrink: 0;
                    }

                    .tip-content {
                        flex: 1;

                        .tip-title {
                            font-weight: 600;
                            color: #333;
                            margin-bottom: 6px;
                            font-size: 14px;
                        }

                        .tip-desc {
                            color: #666;
                            font-size: 13px;
                            line-height: 1.5;
                        }
                    }
                }

                .recognition-area {
                    margin-bottom: 25px;

                    .textarea-label {
                        display: flex;
                        align-items: center;
                        margin-bottom: 10px;
                        color: #333;
                        font-weight: 500;
                        font-size: 14px;

                        .el-icon {
                            margin-right: 6px;
                            color: #409eff;
                        }
                    }

                    .recognition-textarea {
                        width: 100%;

                        :deep(.el-textarea__inner) {
                            border-radius: 8px;
                            border: 2px solid #e8f4fd;
                            font-size: 14px;
                            line-height: 1.6;
                            transition: all 0.3s ease;

                            &:focus {
                                border-color: #409eff;
                                box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
                            }
                        }
                    }

                    .textarea-footer {
                        display: flex;
                        justify-content: flex-end;
                        margin-top: 8px;

                        .char-count {
                            font-size: 12px;
                            color: #999;
                        }
                    }
                }

                .recognition-actions {
                    display: flex;
                    gap: 12px;
                    margin-bottom: 25px;

                    .clear-btn {
                        flex: 1;
                        height: 42px;
                        border-radius: 8px;
                        border: 2px solid #f56c6c;
                        color: #f56c6c;
                        background: white;
                        transition: all 0.3s ease;

                        &:hover {
                            background: #f56c6c;
                            color: white;
                        }
                    }

                    .recognize-btn {
                        flex: 2;
                        height: 42px;
                        border-radius: 8px;
                        background: linear-gradient(135deg, #409eff, #67c23a);
                        border: none;
                        font-weight: 600;
                        transition: all 0.3s ease;

                        &:hover:not(:disabled) {
                            transform: translateY(-2px);
                            box-shadow: 0 6px 16px rgba(64, 158, 255, 0.3);
                        }

                        &:disabled {
                            background: #c0c4cc;
                            cursor: not-allowed;
                        }
                    }
                }

                .recognition-result {
                    padding: 20px;
                    background: linear-gradient(135deg, #f0f9ff, #e8f8f5);
                    border-radius: 8px;
                    border: 1px solid #b3e5d1;
                    animation: slideIn 0.3s ease;

                    .result-header {
                        display: flex;
                        align-items: center;
                        margin-bottom: 15px;

                        .result-icon {
                            color: #67c23a;
                            margin-right: 8px;
                            font-size: 18px;
                        }

                        .result-title {
                            font-weight: 600;
                            color: #333;
                            font-size: 15px;
                        }
                    }

                    .result-content {
                        .result-item {
                            display: flex;
                            margin-bottom: 10px;
                            font-size: 14px;

                            &:last-child {
                                margin-bottom: 0;
                            }

                            label {
                                color: #666;
                                width: 80px;
                                flex-shrink: 0;
                                font-weight: 500;
                            }

                            span {
                                color: #333;
                                word-break: break-all;
                                background: white;
                                padding: 4px 8px;
                                border-radius: 4px;
                                border: 1px solid #e8f4fd;
                            }
                        }
                    }
                }
            }
        }
    }

    // 响应式设计
    @media (max-width: 1000px) {
        .main-content {
            flex-direction: column;

            .left-panel,
            .right-panel {
                max-width: none;
                width: 100%;
            }
        }
    }
}

// Element Plus 样式覆盖
:deep(.el-input__wrapper) {
    border-radius: 4px;
}

:deep(.el-button) {
    border-radius: 4px;
}

:deep(.el-select) {
    width: 100%;

    .el-select__wrapper {
        border-radius: 8px;
        border: 2px solid #e8f4fd;
        transition: all 0.3s ease;

        &:hover {
            border-color: #409eff;
        }
    }
}

// 下拉选项样式
:deep(.el-select-dropdown__item) {
    .option-text {
        display: flex;
        align-items: center;

        .el-icon {
            margin-right: 8px;
            color: #409eff;
        }
    }
}

:deep(.el-textarea__inner) {
    border-radius: 4px;
    resize: none;
}

// 动画效果
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
