<template>
    <div class="dominos-order-container">
        <!-- 头部标题 -->
        <div class="header">
            <h2 class="title">达美乐下单信息</h2>
            <div class="header-actions">
                <el-button type="primary" icon="Download">导出下单</el-button>
                <el-button icon="Delete">清除地址</el-button>
            </div>
        </div>

        <!-- 主要内容区域 - 左右布局 -->
        <div class="main-content">
            <!-- 左侧：订单表单 -->
            <div class="left-panel">
                <div class="order-form">
                    <el-form :model="orderForm" label-width="0px" class="form-content">
                        <!-- 手机号码 -->
                        <el-form-item>
                            <el-input
                                v-model="orderForm.phone"
                                placeholder="手机号码(支持转换号)"
                                class="phone-input"
                            />
                        </el-form-item>

                        <!-- 提示信息 -->
                        <div class="tip-text">
                            联系收件人姓名、手机号、收货地址(要包含省市区)，才可代下单!请确认收货信息。
                        </div>

                        <!-- 客户姓名 -->
                        <el-form-item>
                            <div class="name-input-group">
                                <el-input
                                    v-model="orderForm.customerName"
                                    placeholder="客户姓名(必填)"
                                    class="customer-name-input"
                                />
                                <el-button type="primary" class="add-customer-btn">本人专辑</el-button>
                            </div>
                        </el-form-item>

                        <!-- 完整地址 -->
                        <el-form-item>
                            <el-input
                                v-model="orderForm.fullAddress"
                                placeholder="完整地址市区"
                                class="address-input"
                            />
                        </el-form-item>

                        <!-- 地址收货地址 -->
                        <el-form-item>
                            <div class="delivery-address-group">
                                <el-input
                                    v-model="orderForm.deliveryAddress"
                                    placeholder="地址收货地址"
                                    class="delivery-input"
                                />
                                <el-button type="primary" class="search-btn">搜索</el-button>
                            </div>
                        </el-form-item>

                        <!-- 门牌号信息 -->
                        <el-form-item>
                            <div class="door-number-group">
                                <el-checkbox v-model="orderForm.includeDoorNumber" class="door-checkbox">
                                    门牌号信息 (选中则可，不要多项)
                                </el-checkbox>
                                <div class="door-actions">
                                    <span class="clear-text" @click="clearDoorNumber">清除</span>
                                    <el-button type="text" class="history-btn" @click="loadHistory">一键历史</el-button>
                                </div>
                            </div>
                        </el-form-item>
                    </el-form>
                </div>
            </div>

            <!-- 右侧：订单信息展示或其他功能 -->
            <div class="right-panel">
                <div class="order-preview">
                    <h3 class="preview-title">订单预览</h3>
                    <div class="preview-content">
                        <div class="preview-item" v-if="orderForm.phone">
                            <label>手机号码：</label>
                            <span>{{ orderForm.phone }}</span>
                        </div>
                        <div class="preview-item" v-if="orderForm.customerName">
                            <label>客户姓名：</label>
                            <span>{{ orderForm.customerName }}</span>
                        </div>
                        <div class="preview-item" v-if="orderForm.fullAddress">
                            <label>完整地址：</label>
                            <span>{{ orderForm.fullAddress }}</span>
                        </div>
                        <div class="preview-item" v-if="orderForm.deliveryAddress">
                            <label>收货地址：</label>
                            <span>{{ orderForm.deliveryAddress }}</span>
                        </div>
                        <div class="preview-item" v-if="orderForm.includeDoorNumber">
                            <label>门牌号信息：</label>
                            <span>已选择</span>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="preview-actions">
                        <el-button type="primary" size="large" @click="submitOrder" :disabled="!isFormValid">
                            提交订单
                        </el-button>
                        <el-button size="large" @click="resetForm">
                            重置表单
                        </el-button>
                    </div>
                </div>

                <!-- 历史记录 -->
                <div class="history-section">
                    <h3 class="history-title">最近订单</h3>
                    <div class="history-list">
                        <div class="history-item" v-for="(item, index) in recentOrders" :key="index">
                            <div class="history-info">
                                <div class="history-name">{{ item.customerName }}</div>
                                <div class="history-phone">{{ item.phone }}</div>
                            </div>
                            <el-button type="text" size="small" @click="loadHistoryOrder(item)">
                                使用
                            </el-button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { reactive, computed } from 'vue'

// 订单表单数据
const orderForm = reactive({
    phone: '',
    customerName: '',
    fullAddress: '',
    deliveryAddress: '',
    includeDoorNumber: false
})

// 最近订单历史数据
const recentOrders = reactive([
    {
        customerName: '张三',
        phone: '138****1234',
        fullAddress: '北京市朝阳区xxx街道',
        deliveryAddress: '朝阳区xxx小区1号楼'
    },
    {
        customerName: '李四',
        phone: '139****5678',
        fullAddress: '上海市浦东新区xxx路',
        deliveryAddress: '浦东新区xxx大厦'
    }
])

// 表单验证
const isFormValid = computed(() => {
    return orderForm.phone && orderForm.customerName && orderForm.fullAddress
})

// 导出下单功能
const exportOrder = () => {
    console.log('导出下单:', orderForm)
    // TODO: 实现导出下单逻辑
}

// 清除地址功能
const clearAddress = () => {
    orderForm.fullAddress = ''
    orderForm.deliveryAddress = ''
}

// 添加客户功能
const addCustomer = () => {
    console.log('添加客户:', orderForm.customerName)
    // TODO: 实现添加客户逻辑
}

// 搜索地址功能
const searchAddress = () => {
    console.log('搜索地址:', orderForm.deliveryAddress)
    // TODO: 实现地址搜索逻辑
}

// 清除门牌号
const clearDoorNumber = () => {
    orderForm.includeDoorNumber = false
}

// 一键历史
const loadHistory = () => {
    console.log('加载历史记录')
    // TODO: 实现历史记录加载逻辑
}

// 提交订单
const submitOrder = () => {
    if (!isFormValid.value) {
        return
    }
    console.log('提交订单:', orderForm)
    // TODO: 实现提交订单逻辑
}

// 重置表单
const resetForm = () => {
    orderForm.phone = ''
    orderForm.customerName = ''
    orderForm.fullAddress = ''
    orderForm.deliveryAddress = ''
    orderForm.includeDoorNumber = false
}

// 加载历史订单
const loadHistoryOrder = (order: any) => {
    orderForm.phone = order.phone
    orderForm.customerName = order.customerName
    orderForm.fullAddress = order.fullAddress
    orderForm.deliveryAddress = order.deliveryAddress
}
</script>

<style scoped lang="scss">
.dominos-order-container {
    padding: 20px;
    background-color: #f5f5f5;
    min-height: 100vh;

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        background: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        .title {
            margin: 0;
            color: #333;
            font-size: 18px;
            font-weight: 500;
        }

        .header-actions {
            display: flex;
            gap: 10px;
        }
    }

    .main-content {
        display: flex;
        gap: 20px;
        align-items: flex-start;

        .left-panel {
            flex: 1;
            max-width: 600px;

            .order-form {
                background: white;
                padding: 30px;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

                .form-content {
                    .el-form-item {
                        margin-bottom: 20px;
                    }

                    .phone-input {
                        width: 100%;
                        max-width: 350px;
                    }

                    .tip-text {
                        color: #999;
                        font-size: 14px;
                        margin: 15px 0 25px 0;
                        line-height: 1.5;
                        padding: 10px;
                        background-color: #f8f9fa;
                        border-radius: 4px;
                        border-left: 3px solid #409eff;
                    }

                    .name-input-group {
                        display: flex;
                        gap: 10px;
                        align-items: center;

                        .customer-name-input {
                            flex: 1;
                            max-width: 250px;
                        }

                        .add-customer-btn {
                            flex-shrink: 0;
                        }
                    }

                    .address-input {
                        width: 100%;
                        max-width: 450px;
                    }

                    .delivery-address-group {
                        display: flex;
                        gap: 10px;
                        align-items: center;

                        .delivery-input {
                            flex: 1;
                            max-width: 350px;
                        }

                        .search-btn {
                            flex-shrink: 0;
                        }
                    }

                    .door-number-group {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        width: 100%;

                        .door-checkbox {
                            color: #666;
                        }

                        .door-actions {
                            display: flex;
                            gap: 15px;
                            align-items: center;

                            .clear-text {
                                color: #ff4757;
                                cursor: pointer;
                                font-size: 14px;

                                &:hover {
                                    text-decoration: underline;
                                }
                            }

                            .history-btn {
                                color: #409eff;
                                padding: 0;
                                font-size: 14px;
                            }
                        }
                    }
                }
            }
        }

        .right-panel {
            flex: 0 0 350px;
            display: flex;
            flex-direction: column;
            gap: 20px;

            .order-preview {
                background: white;
                padding: 25px;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

                .preview-title {
                    margin: 0 0 20px 0;
                    color: #333;
                    font-size: 16px;
                    font-weight: 500;
                    border-bottom: 2px solid #409eff;
                    padding-bottom: 8px;
                }

                .preview-content {
                    margin-bottom: 25px;

                    .preview-item {
                        display: flex;
                        margin-bottom: 12px;
                        font-size: 14px;

                        label {
                            color: #666;
                            width: 80px;
                            flex-shrink: 0;
                        }

                        span {
                            color: #333;
                            word-break: break-all;
                        }
                    }
                }

                .preview-actions {
                    display: flex;
                    flex-direction: column;
                    gap: 10px;

                    .el-button {
                        width: 100%;
                    }
                }
            }

            .history-section {
                background: white;
                padding: 25px;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

                .history-title {
                    margin: 0 0 20px 0;
                    color: #333;
                    font-size: 16px;
                    font-weight: 500;
                    border-bottom: 2px solid #67c23a;
                    padding-bottom: 8px;
                }

                .history-list {
                    .history-item {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        padding: 12px 0;
                        border-bottom: 1px solid #f0f0f0;

                        &:last-child {
                            border-bottom: none;
                        }

                        .history-info {
                            flex: 1;

                            .history-name {
                                font-size: 14px;
                                color: #333;
                                margin-bottom: 4px;
                            }

                            .history-phone {
                                font-size: 12px;
                                color: #999;
                            }
                        }
                    }
                }
            }
        }
    }

    // 响应式设计
    @media (max-width: 1200px) {
        .main-content {
            flex-direction: column;

            .right-panel {
                flex: none;
                width: 100%;
            }
        }
    }
}

// Element Plus 样式覆盖
:deep(.el-input__wrapper) {
    border-radius: 4px;
}

:deep(.el-button) {
    border-radius: 4px;
}

:deep(.el-checkbox__label) {
    color: #666;
}
</style>
