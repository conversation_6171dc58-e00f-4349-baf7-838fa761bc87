<template>
    <div class="dominos-order-container">
        <!-- 主要内容区域 - 左右布局 -->
        <div class="main-content">
            <!-- 左上角标题 -->
            <h3 class="section-title">填写下单信息</h3>

            <div class="content-wrapper">
                <!-- 左侧：填写下单信息 -->
                <div class="left-panel">
                <div class="order-form">
                    <el-form :model="orderForm" label-width="80px" class="form-content">
                        <!-- 送餐城市 -->
                        <el-form-item label="送餐城市:">
                            <el-select
                                v-model="orderForm.deliveryCity"
                                placeholder="上海市"
                                class="form-input"
                                clearable
                            >
                                <el-option label="上海市" value="shanghai" />
                                <el-option label="北京市" value="beijing" />
                                <el-option label="广州市" value="guangzhou" />
                                <el-option label="深圳市" value="shenzhen" />
                            </el-select>
                        </el-form-item>

                        <!-- 送餐地址 -->
                        <el-form-item label="送餐地址:">
                            <div class="delivery-address-group">
                                <el-input
                                    v-model="orderForm.deliveryAddress"
                                    placeholder="地址或小区写字楼或学校"
                                    class="delivery-input"
                                />
                                <el-button type="primary" class="search-btn">搜索</el-button>
                            </div>
                        </el-form-item>

                        <!-- 详细地址 -->
                        <el-form-item label="详细地址:">
                            <el-input
                                v-model="orderForm.detailAddress"
                                placeholder="楼号/门牌号，例：1号101室"
                                class="form-input"
                            />
                        </el-form-item>



                        <!-- 收货人 -->
                        <el-form-item label="*收货人:">
                            <el-input
                                v-model="orderForm.receiverName"
                                placeholder="请输入收货人姓名"
                                class="form-input"
                            />
                        </el-form-item>

                        <!-- 联系电话 -->
                        <el-form-item label="*联系电话:">
                            <el-input
                                v-model="orderForm.phone"
                                placeholder="请输入联系电话"
                                class="form-input"
                            />
                        </el-form-item>

                        <!-- 送餐门店 -->
                        <el-form-item label="*送餐门店:">
                            <el-input
                                v-model="orderForm.store"
                                placeholder="请选择门店"
                                class="form-input"
                                readonly
                            />
                        </el-form-item>
                    </el-form>
                </div>
            </div>

            <!-- 右侧：快速识别地址 -->
            <div class="right-panel">
                <div class="address-recognition">
                    <!-- 提示信息 -->
                    <div class="tip-text">
                        粘贴收件人姓名、手机号、收货地址(要包含省市区)，才可快速识别收货信息。
                    </div>

                    <!-- 识别文本区域 -->
                    <div class="recognition-area">
                        <el-input
                            v-model="recognitionText"
                            type="textarea"
                            :rows="8"
                            placeholder="请粘贴收件人信息..."
                            class="recognition-textarea"
                        />
                    </div>

                    <!-- 操作按钮 -->
                    <div class="recognition-actions">
                        <span class="clear-text" @click="clearRecognition">清除</span>
                        <el-button type="primary" @click="recognizeAddress">一键识别</el-button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'

// 订单表单数据
const orderForm = reactive({
    deliveryCity: 'shanghai',
    deliveryAddress: '',
    detailAddress: '',
    receiverName: '',
    phone: '',
    store: ''
})

// 识别文本
const recognitionText = ref('')

// 添加客户功能
const addCustomer = () => {
    console.log('添加客户:', orderForm.customerName)
    // TODO: 实现添加客户逻辑
}

// 搜索地址功能
const searchAddress = () => {
    console.log('搜索地址:', orderForm.deliveryAddress)
    // TODO: 实现地址搜索逻辑
}

// 清除识别文本
const clearRecognition = () => {
    recognitionText.value = ''
}

// 一键识别地址
const recognizeAddress = () => {
    if (!recognitionText.value.trim()) {
        return
    }

    // 简单的地址识别逻辑（可以根据实际需求完善）
    const text = recognitionText.value

    // 识别手机号
    const phoneMatch = text.match(/1[3-9]\d{9}/)
    if (phoneMatch) {
        orderForm.phone = phoneMatch[0]
    }

    // 识别姓名（假设姓名在手机号前面，2-4个字符）
    const nameMatch = text.match(/([^\d\s]{2,4})\s*1[3-9]\d{9}/)
    if (nameMatch) {
        orderForm.customerName = nameMatch[1]
    }

    // 识别地址（包含省市区的部分）
    const addressMatch = text.match(/([\u4e00-\u9fa5]+[省市区县][\u4e00-\u9fa5\d\s]+)/)
    if (addressMatch) {
        orderForm.deliveryAddress = addressMatch[1]
    }

    // 识别收货人姓名（假设姓名在手机号前面，2-4个字符）
    if (nameMatch) {
        orderForm.receiverName = nameMatch[1]
    }

    console.log('识别结果:', orderForm)
}
</script>

<style scoped lang="scss">
.dominos-order-container {
    padding: 20px;
    background-color: #f5f5f5;
    min-height: 100vh;



    .main-content {
        display: flex;
        justify-content: center;
        align-items: flex-start;

        .section-title {
            margin: 0 0 20px 0;
            color: #333;
            font-size: 18px;
            font-weight: 500;
        }

        .content-wrapper {
            display: flex;
            gap: 20px;
            align-items: stretch;
            width: 100%;
        }

        .left-panel {
            flex: 1;
            width: 50%;
            min-width: 400px;
            display: flex;
            flex-direction: column;

            .order-form {
                background: white;
                padding: 25px;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                flex: 1;
                display: flex;
                flex-direction: column;

                .form-title {
                    margin: 0 0 25px 0;
                    color: #333;
                    font-size: 16px;
                    font-weight: 500;
                }

                .form-content {
                    .el-form-item {
                        margin-bottom: 20px;
                    }

                    .form-input {
                        width: 100%;
                    }

                    .delivery-address-group {
                        display: flex;
                        gap: 10px;
                        align-items: center;

                        .delivery-input {
                            flex: 1;
                        }

                        .search-btn {
                            flex-shrink: 0;
                        }
                    }


                }
            }
        }

        .right-panel {
            flex: 1;
            width: 50%;
            min-width: 400px;
            display: flex;
            flex-direction: column;

            .address-recognition {
                background: white;
                padding: 25px;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                flex: 1;
                display: flex;
                flex-direction: column;

                .tip-text {
                    color: #666;
                    font-size: 14px;
                    line-height: 1.5;
                    margin-bottom: 20px;
                    padding: 15px;
                    background-color: #f8f9fa;
                    border-radius: 4px;
                    border-left: 3px solid #409eff;
                }

                .recognition-area {
                    margin-bottom: 20px;

                    .recognition-textarea {
                        width: 100%;
                    }
                }

                .recognition-actions {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    .clear-text {
                        color: #ff4757;
                        cursor: pointer;
                        font-size: 14px;

                        &:hover {
                            text-decoration: underline;
                        }
                    }
                }
            }
        }
    }

    // 响应式设计
    @media (max-width: 1000px) {
        .main-content {
            .content-wrapper {
                flex-direction: column;

                .left-panel,
                .right-panel {
                    max-width: none;
                    width: 100%;
                }
            }
        }
    }
}

// Element Plus 样式覆盖
:deep(.el-input__wrapper) {
    border-radius: 4px;
}

:deep(.el-button) {
    border-radius: 4px;
}

:deep(.el-select) {
    width: 100%;
}

:deep(.el-textarea__inner) {
    border-radius: 4px;
    resize: none;
}
</style>
