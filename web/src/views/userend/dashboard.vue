<template>
    <div class="dominos-order-container">
        <!-- 主要内容区域 - 左右布局 -->
        <div class="main-content">
            <!-- 左侧：填写下单信息 -->
            <div class="left-panel">
                <div class="order-form">
                    <h3 class="form-title">填写下单信息</h3>
                    <el-form :model="orderForm" label-width="0px" class="form-content">
                        <!-- 手机号码 -->
                        <el-form-item>
                            <el-input
                                v-model="orderForm.phone"
                                placeholder="手机号码(支持转换号)"
                                class="form-input"
                            />
                        </el-form-item>

                        <!-- 客户姓名 -->
                        <el-form-item>
                            <div class="name-input-group">
                                <el-input
                                    v-model="orderForm.customerName"
                                    placeholder="客户姓名(选填)"
                                    class="customer-name-input"
                                />
                                <el-button type="primary" class="add-customer-btn">本人专辑</el-button>
                            </div>
                        </el-form-item>

                        <!-- 选择省市区 -->
                        <el-form-item>
                            <el-select
                                v-model="orderForm.cityArea"
                                placeholder="选择省市区"
                                class="form-input"
                                clearable
                            >
                                <el-option label="北京市朝阳区" value="beijing-chaoyang" />
                                <el-option label="上海市浦东新区" value="shanghai-pudong" />
                                <el-option label="广州市天河区" value="guangzhou-tianhe" />
                                <el-option label="深圳市南山区" value="shenzhen-nanshan" />
                            </el-select>
                        </el-form-item>

                        <!-- 选择收货地址 -->
                        <el-form-item>
                            <div class="delivery-address-group">
                                <el-input
                                    v-model="orderForm.deliveryAddress"
                                    placeholder="选择收货地址"
                                    class="delivery-input"
                                />
                                <el-button type="primary" class="search-btn">搜索</el-button>
                            </div>
                        </el-form-item>

                        <!-- 门牌号信息 -->
                        <el-form-item>
                            <el-input
                                v-model="orderForm.doorNumber"
                                placeholder="门牌号信息 (选中则可，不要多项)"
                                class="form-input"
                            />
                        </el-form-item>
                    </el-form>
                </div>
            </div>

            <!-- 右侧：快速识别地址 -->
            <div class="right-panel">
                <div class="address-recognition">
                    <!-- 提示信息 -->
                    <div class="tip-text">
                        粘贴收件人姓名、手机号、收货地址(要包含省市区)，才可快速识别收货信息。
                    </div>

                    <!-- 识别文本区域 -->
                    <div class="recognition-area">
                        <el-input
                            v-model="recognitionText"
                            type="textarea"
                            :rows="8"
                            placeholder="请粘贴收件人信息..."
                            class="recognition-textarea"
                        />
                    </div>

                    <!-- 操作按钮 -->
                    <div class="recognition-actions">
                        <span class="clear-text" @click="clearRecognition">清除</span>
                        <el-button type="primary" @click="recognizeAddress">一键识别</el-button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'

// 订单表单数据
const orderForm = reactive({
    phone: '',
    customerName: '',
    cityArea: '',
    deliveryAddress: '',
    doorNumber: ''
})

// 识别文本
const recognitionText = ref('')

// 添加客户功能
const addCustomer = () => {
    console.log('添加客户:', orderForm.customerName)
    // TODO: 实现添加客户逻辑
}

// 搜索地址功能
const searchAddress = () => {
    console.log('搜索地址:', orderForm.deliveryAddress)
    // TODO: 实现地址搜索逻辑
}

// 清除识别文本
const clearRecognition = () => {
    recognitionText.value = ''
}

// 一键识别地址
const recognizeAddress = () => {
    if (!recognitionText.value.trim()) {
        return
    }

    // 简单的地址识别逻辑（可以根据实际需求完善）
    const text = recognitionText.value

    // 识别手机号
    const phoneMatch = text.match(/1[3-9]\d{9}/)
    if (phoneMatch) {
        orderForm.phone = phoneMatch[0]
    }

    // 识别姓名（假设姓名在手机号前面，2-4个字符）
    const nameMatch = text.match(/([^\d\s]{2,4})\s*1[3-9]\d{9}/)
    if (nameMatch) {
        orderForm.customerName = nameMatch[1]
    }

    // 识别地址（包含省市区的部分）
    const addressMatch = text.match(/([\u4e00-\u9fa5]+[省市区县][\u4e00-\u9fa5\d\s]+)/)
    if (addressMatch) {
        orderForm.deliveryAddress = addressMatch[1]
    }

    console.log('识别结果:', orderForm)
}
</script>

<style scoped lang="scss">
.dominos-order-container {
    padding: 20px;
    background-color: #f5f5f5;
    min-height: 100vh;

    .main-content {
        display: flex;
        gap: 20px;
        align-items: flex-start;

        .left-panel {
            flex: 1;
            max-width: 500px;

            .order-form {
                background: white;
                padding: 25px;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

                .form-title {
                    margin: 0 0 25px 0;
                    color: #333;
                    font-size: 16px;
                    font-weight: 500;
                }

                .form-content {
                    .el-form-item {
                        margin-bottom: 20px;
                    }

                    .form-input {
                        width: 100%;
                    }

                    .name-input-group {
                        display: flex;
                        gap: 10px;
                        align-items: center;

                        .customer-name-input {
                            flex: 1;
                        }

                        .add-customer-btn {
                            flex-shrink: 0;
                        }
                    }

                    .delivery-address-group {
                        display: flex;
                        gap: 10px;
                        align-items: center;

                        .delivery-input {
                            flex: 1;
                        }

                        .search-btn {
                            flex-shrink: 0;
                        }
                    }
                }
            }
        }

        .right-panel {
            flex: 1;
            max-width: 500px;

            .address-recognition {
                background: white;
                padding: 25px;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

                .tip-text {
                    color: #666;
                    font-size: 14px;
                    line-height: 1.5;
                    margin-bottom: 20px;
                    padding: 15px;
                    background-color: #f8f9fa;
                    border-radius: 4px;
                    border-left: 3px solid #409eff;
                }

                .recognition-area {
                    margin-bottom: 20px;

                    .recognition-textarea {
                        width: 100%;
                    }
                }

                .recognition-actions {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    .clear-text {
                        color: #ff4757;
                        cursor: pointer;
                        font-size: 14px;

                        &:hover {
                            text-decoration: underline;
                        }
                    }
                }
            }
        }
    }

    // 响应式设计
    @media (max-width: 1000px) {
        .main-content {
            flex-direction: column;

            .left-panel,
            .right-panel {
                max-width: none;
                width: 100%;
            }
        }
    }
}

// Element Plus 样式覆盖
:deep(.el-input__wrapper) {
    border-radius: 4px;
}

:deep(.el-button) {
    border-radius: 4px;
}

:deep(.el-select) {
    width: 100%;
}

:deep(.el-textarea__inner) {
    border-radius: 4px;
    resize: none;
}
</style>
