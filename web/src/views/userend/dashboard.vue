<template>
    <div class="dominos-order-container">
        <!-- 头部标题 -->
        <div class="header">
            <h2 class="title">达美乐下单信息</h2>
            <div class="header-actions">
                <el-button type="primary" icon="Download">导出下单</el-button>
                <el-button icon="Delete">清除地址</el-button>
            </div>
        </div>

        <!-- 订单表单 -->
        <div class="order-form">
            <el-form :model="orderForm" label-width="120px" class="form-content">
                <!-- 手机号码 -->
                <el-form-item label="">
                    <el-input
                        v-model="orderForm.phone"
                        placeholder="手机号码(支持转换号)"
                        class="phone-input"
                    />
                </el-form-item>

                <!-- 提示信息 -->
                <div class="tip-text">
                    联系收件人姓名、手机号、收货地址(要包含省市区)，才可代下单!请确认收货信息。
                </div>

                <!-- 客户姓名 -->
                <el-form-item label="">
                    <div class="name-input-group">
                        <el-input
                            v-model="orderForm.customerName"
                            placeholder="客户姓名(必填)"
                            class="customer-name-input"
                        />
                        <el-button type="primary" class="add-customer-btn">本人专辑</el-button>
                    </div>
                </el-form-item>

                <!-- 完整地址 -->
                <el-form-item label="">
                    <el-input
                        v-model="orderForm.fullAddress"
                        placeholder="完整地址市区"
                        class="address-input"
                    />
                </el-form-item>

                <!-- 地址收货地址 -->
                <el-form-item label="">
                    <div class="delivery-address-group">
                        <el-input
                            v-model="orderForm.deliveryAddress"
                            placeholder="地址收货地址"
                            class="delivery-input"
                        />
                        <el-button type="primary" class="search-btn">搜索</el-button>
                    </div>
                </el-form-item>

                <!-- 门牌号信息 -->
                <el-form-item label="">
                    <div class="door-number-group">
                        <el-checkbox v-model="orderForm.includeDoorNumber" class="door-checkbox">
                            门牌号信息 (选中则可，不要多项)
                        </el-checkbox>
                        <div class="door-actions">
                            <span class="clear-text">清除</span>
                            <el-button type="text" class="history-btn">一键历史</el-button>
                        </div>
                    </div>
                </el-form-item>
            </el-form>
        </div>
    </div>
</template>

<script setup lang="ts">
import { reactive } from 'vue'

// 订单表单数据
const orderForm = reactive({
    phone: '',
    customerName: '',
    fullAddress: '',
    deliveryAddress: '',
    includeDoorNumber: false
})

// 导出下单功能
const exportOrder = () => {
    console.log('导出下单:', orderForm)
    // TODO: 实现导出下单逻辑
}

// 清除地址功能
const clearAddress = () => {
    orderForm.fullAddress = ''
    orderForm.deliveryAddress = ''
}

// 添加客户功能
const addCustomer = () => {
    console.log('添加客户:', orderForm.customerName)
    // TODO: 实现添加客户逻辑
}

// 搜索地址功能
const searchAddress = () => {
    console.log('搜索地址:', orderForm.deliveryAddress)
    // TODO: 实现地址搜索逻辑
}

// 清除门牌号
const clearDoorNumber = () => {
    orderForm.includeDoorNumber = false
}

// 一键历史
const loadHistory = () => {
    console.log('加载历史记录')
    // TODO: 实现历史记录加载逻辑
}
</script>

<style scoped lang="scss">
.dominos-order-container {
    padding: 20px;
    background-color: #f5f5f5;
    min-height: 100vh;

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        background: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        .title {
            margin: 0;
            color: #333;
            font-size: 18px;
            font-weight: 500;
        }

        .header-actions {
            display: flex;
            gap: 10px;
        }
    }

    .order-form {
        background: white;
        padding: 30px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        .form-content {
            max-width: 800px;

            .el-form-item {
                margin-bottom: 20px;
            }

            .phone-input {
                width: 300px;
            }

            .tip-text {
                color: #999;
                font-size: 14px;
                margin: 15px 0 25px 0;
                line-height: 1.5;
            }

            .name-input-group {
                display: flex;
                gap: 10px;
                align-items: center;

                .customer-name-input {
                    width: 300px;
                }

                .add-customer-btn {
                    flex-shrink: 0;
                }
            }

            .address-input {
                width: 400px;
            }

            .delivery-address-group {
                display: flex;
                gap: 10px;
                align-items: center;

                .delivery-input {
                    width: 400px;
                }

                .search-btn {
                    flex-shrink: 0;
                }
            }

            .door-number-group {
                display: flex;
                justify-content: space-between;
                align-items: center;
                width: 100%;

                .door-checkbox {
                    color: #666;
                }

                .door-actions {
                    display: flex;
                    gap: 15px;
                    align-items: center;

                    .clear-text {
                        color: #ff4757;
                        cursor: pointer;
                        font-size: 14px;

                        &:hover {
                            text-decoration: underline;
                        }
                    }

                    .history-btn {
                        color: #409eff;
                        padding: 0;
                        font-size: 14px;
                    }
                }
            }
        }
    }
}

// Element Plus 样式覆盖
:deep(.el-input__wrapper) {
    border-radius: 4px;
}

:deep(.el-button) {
    border-radius: 4px;
}

:deep(.el-checkbox__label) {
    color: #666;
}
</style>
